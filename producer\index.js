const { Queue } = require('bullmq');
const IORedis = require('ioredis');

const connection = new IORedis(process.env.REDIS_CONNECTION_STRING);

const urls = [
  "https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige.html",
  "https://www.xboxaktuell.de/news,id25348,screamer_erstes_gameplay_arcade_racer.html",
  "https://www.gusto.at"
];

// 🧪 Redis-Healthcheck (pingt bis Redis antwortet)
async function waitForRedisReady(maxTries = 10) {
  for (let i = 0; i < maxTries; i++) {
    try {
      const pong = await connection.ping();
      if (pong === 'PONG') {
        console.log('✅ Redis ist bereit!');
        return;
      }
    } catch (err) {
      console.log(`🕐 Redis noch nicht bereit... Versuch ${i + 1}`);
    }
    await new Promise((r) => setTimeout(r, 1000));
  }
  throw new Error('❌ Redis nicht erreichbar – Producer bricht ab.');
}

async function main() {
  await waitForRedisReady();

  const queue = new Queue('url-jobs', { connection });

  for (const url of urls) {
    await queue.add('process-url', { url });
    console.log(`📬 Job hinzugefügt: ${url}`);
  }

  console.log('🚀 Alle Jobs eingereiht. Producer beendet sich.');
  process.exit(0);
}

main();