export const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector("#cmpwrapper", { timeout: 5000 });

    const shadowHost = await page.$("#cmpwrapper");
    const shadowRootHandle = await shadowHost.evaluateHandle(
      (el) => el.shadowRoot
    );
    if (!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector(".cmptxt_btn_yes"),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log("❌ Consent button not found in shadow root");
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log("✅ Consent button clicked");
      return 1;
    } else {
      console.log("❌ Consent button not found as element");
      return 0;
    }
  } catch (err) {
    console.error("⚠️ Error during cookie consent:", err.message);
    return -1;
  }
};

export const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log("QMN-Objekt oder qmn.adSlots ist nicht im erwarteten Format.");
    return {};
  }

  return qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Anzeigen vom Typ 'tracker' und Slots ohne ID/Responsive ignorieren
    if (!id || !responsive || type === "tracker") {
      return acc;
    }

    // Sicherstellen, dass das Array für den Gerätetyp existiert
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Anzeigen-Objekt zur Liste hinzufügen
    acc[responsive].push({ id, type });

    return acc;
  }, {});
};

export const checkSelectors = async (page) => {
  const qmnSelectors = await page.evaluate(() => {
    return window.qmn;
  });

  console.log("QMN Selectors:", JSON.stringify(qmnSelectors, null, 2));

  if (!qmnSelectors) {
    console.log("⚠️ window.qmn-Objekt nicht auf der Seite gefunden.");
    return null;
  }

  return qmnSelectors.config;
};

export const findMissingAds = (ads) => {
  const missing = [];
  for (const ad of ads) {
    const adContainer = document.getElementById(`qmn${ad.id}`);
    if (!adContainer) {
      missing.push(ad);
    }
  }
  return missing;
};

export const setupPageForAdCheck = async (page, config) => {
  await page.setViewport({ width: config.width, height: config.height });
  await page.reload({ waitUntil: "networkidle2" });

  // Scroll down to trigger lazy-loaded ads
  await page.evaluate(() =>
    window.scrollTo(0, document.body.scrollHeight)
  );
  await new Promise((resolve) => setTimeout(resolve, 2000));
};

 