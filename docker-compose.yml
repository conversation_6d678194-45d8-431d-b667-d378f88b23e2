name: pupquer
services:
  redis:
    image: redis:6
    ports:
      - "6379:6379"

  producer:
    build: ./producer
    depends_on:
      redis:
        condition: service_started
    environment:
      REDIS_CONNECTION_STRING: ${REDIS_CONNECTION_STRING}
      
  worker:
    build: ./worker-version-1
    depends_on:
      redis:
        condition: service_started
    environment:
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
    volumes:
      - ./worker-version-1/index.js:/app/index.js
      - ./worker-version-1/package.json:/app/package.json
      - ./worker-version-1/utils.js:/app/utils.js
    deploy:
      replicas: 3
  bullboard:
    image: deadly0/bull-board:latest
    platform: linux/amd64
    ports:
      - "3000:3000"
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      QUEUES: url-jobs
      QUEUE_TYPE: bullmq
      UI_PATH: /
    depends_on:
      redis:
        condition: service_started
