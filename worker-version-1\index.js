// Test change to verify volume mounting is working

console.log("Worker started with volume mounting test - FIXED ERROR 3");

import {Worker} from "bullmq";
import IORedis from "ioredis";
import puppeteer from "puppeteer";
import {
  acceptCookieConsent,
  groupAdsByResponsive,
  checkSelectors,
  findMissingAds,
  setupPageForAdCheck,
} from "./utils";


/* const { Worker } = require("bullmq");
const IORedis = require("ioredis");
const puppeteer = require("puppeteer"); */
/* const {
  acceptCookieConsent,
  groupAdsByResponsive,
  checkSelectors,
  findMissingAds,
  setupPageForAdCheck,
} = require("./utils"); */

const connection = new IORedis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT, 10),
  maxRetriesPerRequest: null,
});

const viewports = {
  mobile: { width: 375, height: 812, deviceType: "mobile", name: "Mobile" },
  tablet: { width: 768, height: 1024, deviceType: "desktop", name: "Tablet" },
  desktop: { width: 1920, height: 1080, deviceType: "desktop", name: "Desktop HD" },
};


const worker = new Worker(
  "url-jobs",
  async (job) => {
    const url = job.data.url;
    console.log(`📥 Starte Analyse: ${url}`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: "new",
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();

    try {
      await page.goto(url, { waitUntil: "networkidle2" });

      // Accept cookie consent if present
      const cookieConsentAccepted = await acceptCookieConsent(page);

      if (cookieConsentAccepted !== 1) {
        throw new Error(
          "Cookie-Zustimmung fehlgeschlagen oder nicht gefunden: " +
            cookieConsentAccepted
        );
      }
      console.log(
        "Cookie-Zustimmung akzeptiert, warte auf mögliche Overlays..."
      );
      
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const qmnSelectors = await checkSelectors(page);
      const adsByResponsive = groupAdsByResponsive(qmnSelectors);

      const missingAds = {
        desktop: new Map(),
        mobile: new Map(),
      };
      let allAdsSuccessfullyPlaced = true;

      for (const [deviceName, config] of Object.entries(viewports)) {
        console.log(
          `\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`
        );

        await setupPageForAdCheck(page, config);

        const responsiveKey = config.deviceType; // 'desktop' or 'mobile'
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(
          `[${deviceName}] Überprüfe folgende Anzeigen für Responsive-Typ '${responsiveKey}':`,
          JSON.stringify(adsToCheck, null, 2)
        );

        if (adsToCheck.length === 0) {
          console.log(
            `[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' gefunden.`
          );
          continue;
        }

        const missingAdsForDevice = await page.evaluate(
          findMissingAds,
          adsToCheck
        );

        if (missingAdsForDevice.length > 0) {
          allAdsSuccessfullyPlaced = false;
          console.log(
            `[${deviceName}] Fehlende Anzeigen:`,
            missingAdsForDevice
          );
          missingAdsForDevice.forEach((ad) =>
            missingAds[responsiveKey].set(ad.id, ad)
          );
        } else {
          console.log(
            `[${deviceName}] Alle ${adsToCheck.length} Anzeigen wurden erfolgreich gefunden.`
          );
        }
      }

      const endTime = Date.now();
      const durationMs = endTime - startTime;

      let finalResult;

      if (allAdsSuccessfullyPlaced) {
        finalResult = {
          success: true,
          url,
          timestamp: new Date().toISOString(),
          processingTimeMs: durationMs,
        };
      } else {
        finalResult = {
          success: false,
          missingAds: {
            desktop: Array.from(missingAds.desktop.values()),
            mobile: Array.from(missingAds.mobile.values()),
          },
          url,
          timestamp: new Date().toISOString(),
          processingTimeMs: durationMs,
        };
      }

      console.log("final result 📈 :", JSON.stringify(finalResult, null, 2));

      return finalResult;
    } catch (e) {
      console.error(`❌ Fehler bei ${url}:`, e.message);
      return {
        url,
        error: e.message,
        timestamp: new Date().toISOString(),
      };
    } finally {
      await browser.close();
    }
  },
  { connection }
);

worker.on("completed", (job) => {
  console.log(`🎉 Job abgeschlossen: ${job.id}`);
});

worker.on("failed", (job, err) => {
  console.error(`❌ Job fehlgeschlagen: ${job.id} - ${err.message}`);
});
